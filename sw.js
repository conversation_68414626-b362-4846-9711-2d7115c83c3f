// Service Worker for Healthcare Management System
// نظام إدارة المراكز الصحية - Service Worker

const CACHE_NAME = 'healthcare-management-v1.0.0';
const STATIC_CACHE_NAME = 'healthcare-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'healthcare-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/index.html',
    '/index.php',
    '/js/api-client.js',
    '/js/app-migration.js',
    '/favicon.svg',
    '/robots.txt',
    '/sitemap.xml'
];

// External resources to cache
const EXTERNAL_RESOURCES = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static files
            caches.open(STATIC_CACHE_NAME).then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            // Cache external resources
            caches.open(CACHE_NAME).then(cache => {
                console.log('Service Worker: Caching external resources');
                return cache.addAll(EXTERNAL_RESOURCES);
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME && 
                        cacheName !== STATIC_CACHE_NAME && 
                        cacheName !== DYNAMIC_CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip API calls and dynamic content
    if (url.pathname.includes('/api/') || 
        url.pathname.includes('.php') ||
        url.pathname.includes('auth.php') ||
        url.pathname.includes('session-check.php')) {
        return;
    }
    
    event.respondWith(
        caches.match(request).then(cachedResponse => {
            if (cachedResponse) {
                console.log('Service Worker: Serving from cache:', request.url);
                return cachedResponse;
            }
            
            // Not in cache, fetch from network
            return fetch(request).then(response => {
                // Don't cache if not a valid response
                if (!response || response.status !== 200 || response.type !== 'basic') {
                    return response;
                }
                
                // Clone the response
                const responseToCache = response.clone();
                
                // Determine which cache to use
                let cacheName = DYNAMIC_CACHE_NAME;
                if (STATIC_FILES.includes(url.pathname) || 
                    EXTERNAL_RESOURCES.includes(request.url)) {
                    cacheName = STATIC_CACHE_NAME;
                }
                
                // Add to cache
                caches.open(cacheName).then(cache => {
                    console.log('Service Worker: Caching new resource:', request.url);
                    cache.put(request, responseToCache);
                });
                
                return response;
            }).catch(() => {
                // Network failed, try to serve offline page for HTML requests
                if (request.headers.get('accept').includes('text/html')) {
                    return caches.match('/index.html');
                }
            });
        })
    );
});

// Background sync for offline functionality
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered');
    
    if (event.tag === 'background-sync') {
        event.waitUntil(
            // Handle offline data sync here
            console.log('Service Worker: Syncing offline data...')
        );
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'إشعار جديد من نظام إدارة المراكز الصحية',
        icon: '/favicon.svg',
        badge: '/favicon.svg',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/favicon.svg'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/favicon.svg'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('نظام إدارة المراكز الصحية', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', event => {
    console.log('Service Worker: Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Error handling
self.addEventListener('error', event => {
    console.error('Service Worker: Error occurred:', event.error);
});

// Unhandled rejection handling
self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker: Unhandled promise rejection:', event.reason);
    event.preventDefault();
});

console.log('Service Worker: Script loaded successfully');
